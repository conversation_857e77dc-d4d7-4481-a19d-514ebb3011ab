<template>
  <div>
    <h1 class="text-3xl font-bold mb-8">Checkout</h1>
    
    <div v-if="!cartItems.length" class="text-center py-12 bg-white rounded-lg shadow-md">
      <Icon name="mdi:cart-outline" class="w-16 h-16 mx-auto text-gray-400 mb-4" />
      <h2 class="text-2xl font-semibold mb-2">Your cart is empty</h2>
      <p class="text-gray-600 mb-6">You need to add items to your cart before checking out.</p>
      <NuxtLink to="/products" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition">
        Start Shopping
      </NuxtLink>
    </div>
    
    <div v-else class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 class="text-xl font-bold mb-4">Shipping Information</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="firstName" class="block text-gray-700 mb-2">First Name</label>
              <input 
                id="firstName" 
                v-model="shippingInfo.firstName" 
                type="text" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div>
              <label for="lastName" class="block text-gray-700 mb-2">Last Name</label>
              <input 
                id="lastName" 
                v-model="shippingInfo.lastName" 
                type="text" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div class="md:col-span-2">
              <label for="address" class="block text-gray-700 mb-2">Address</label>
              <input 
                id="address" 
                v-model="shippingInfo.address" 
                type="text" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div>
              <label for="city" class="block text-gray-700 mb-2">City</label>
              <input 
                id="city" 
                v-model="shippingInfo.city" 
                type="text" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div>
              <label for="zipCode" class="block text-gray-700 mb-2">ZIP Code</label>
              <input 
                id="zipCode" 
                v-model="shippingInfo.zipCode" 
                type="text" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div>
              <label for="country" class="block text-gray-700 mb-2">Country</label>
              <select 
                id="country" 
                v-model="shippingInfo.country" 
                class="w-full border border-gray-300 rounded px-3 py-2"
              >
                <option value="US">United States</option>
                <option value="CA">Canada</option>
                <option value="UK">United Kingdom</option>
                <option value="AU">Australia</option>
              </select>
            </div>
            <div>
              <label for="phone" class="block text-gray-700 mb-2">Phone</label>
              <input 
                id="phone" 
                v-model="shippingInfo.phone" 
                type="tel" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Payment Information</h2>
          <div class="grid grid-cols-1 gap-4">
            <div>
              <label for="cardName" class="block text-gray-700 mb-2">Name on Card</label>
              <input 
                id="cardName" 
                v-model="paymentInfo.cardName" 
                type="text" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div>
              <label for="cardNumber" class="block text-gray-700 mb-2">Card Number</label>
              <input 
                id="cardNumber" 
                v-model="paymentInfo.cardNumber" 
                type="text" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label for="expiryDate" class="block text-gray-700 mb-2">Expiry Date</label>
                <input 
                  id="expiryDate" 
                  v-model="paymentInfo.expiryDate" 
                  type="text" 
                  placeholder="MM/YY" 
                  class="w-full border border-gray-300 rounded px-3 py-2" 
                />
              </div>
              <div>
                <label for="cvv" class="block text-gray-700 mb-2">CVV</label>
                <input 
                  id="cvv" 
                  v-model="paymentInfo.cvv" 
                  type="text" 
                  class="w-full border border-gray-300 rounded px-3 py-2" 
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-md p-6 sticky top-6">
          <h2 class="text-xl font-bold mb-4">Order Summary</h2>
          <div class="space-y-3 mb-4">
            <div v-for="item in cartItems" :key="item.productId" class="flex justify-between">
              <div>
                <span class="font-medium">{{ item.product.name }}</span>
                <span class="text-gray-600 text-sm block">Qty: {{ item.quantity }}</span>
              </div>
              <span>${{ (item.product.price * item.quantity).toFixed(2) }}</span>
            </div>
          </div>
          
          <div class="border-t border-gray-200 my-4"></div>
          
          <div class="flex justify-between mb-2">
            <span class="text-gray-600">Subtotal</span>
            <span>${{ cartTotal.toFixed(2) }}</span>
          </div>
          <div class="flex justify-between mb-2">
            <span class="text-gray-600">Shipping</span>
            <span>$0.00</span>
          </div>
          <div class="flex justify-between mb-2">
            <span class="text-gray-600">Tax</span>
            <span>${{ (cartTotal * 0.1).toFixed(2) }}</span>
          </div>
          
          <div class="border-t border-gray-200 my-4"></div>
          
          <div class="flex justify-between mb-6">
            <span class="text-lg font-bold">Total</span>
            <span class="text-lg font-bold">${{ (cartTotal + cartTotal * 0.1).toFixed(2) }}</span>
          </div>
          
          <button 
            @click="placeOrder" 
            class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition"
            :disabled="isSubmitting"
          >
            {{ isSubmitting ? 'Processing...' : 'Place Order' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useCartStore } from '~/stores/cart'

const cartStore = useCartStore()
const cartItems = storeToRefs(cartStore).items
const cartTotal = storeToRefs(cartStore).total

const router = useRouter()
const isSubmitting = ref(false)

const shippingInfo = reactive({
  firstName: '',
  lastName: '',
  address: '',
  city: '',
  zipCode: '',
  country: 'US',
  phone: ''
})

const paymentInfo = reactive({
  cardName: '',
  cardNumber: '',
  expiryDate: '',
  cvv: ''
})

const placeOrder = async () => {
  isSubmitting.value = true
  
  try {
    // In a real app, you would send the order to your backend
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Clear the cart
    cartStore.clearCart()
    
    // Redirect to success page
    router.push('/checkout/success')
  } catch (error) {
    console.error('Error placing order:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>