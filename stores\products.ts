import { defineStore } from 'pinia'

interface Product {
  id: number
  name: string
  description: string
  price: number
  image: string
  category: string
}

export const useProductStore = defineStore('products', {
  state: () => ({
    products: [] as Product[],
    loading: false,
    error: null as string | null
  }),
  
  getters: {
    getProductById: (state) => (id: number) => {
      return state.products.find(product => product.id === id)
    }
  },
  
  actions: {
    async fetchProducts() {
      this.loading = true
      this.error = null
      
      try {
        // Fetch products from the JSON file
        const response = await fetch('/mock/products.json')
        if (!response.ok) {
          throw new Error('Failed to fetch products')
        }
        
        this.products = await response.json()
      } catch (error) {
        this.error = 'Failed to fetch products'
        console.error(error)
      } finally {
        this.loading = false
      }
    }
  }
})