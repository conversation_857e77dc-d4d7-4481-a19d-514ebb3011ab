<template>
  <div>
    <section class="mb-12">
      <div class="bg-blue-600 text-white rounded-lg p-8 text-center">
        <h1 class="text-4xl font-bold mb-4">Welcome to NuxtMarkt</h1>
        <p class="text-xl mb-6">Your one-stop shop for all your needs</p>
        <NuxtLink to="/products" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 transition">
          Shop Now
        </NuxtLink>
      </div>
    </section>
    
    <section class="mb-12">
      <h2 class="text-2xl font-bold mb-6">Featured Products</h2>
      <div v-if="loading" class="text-center py-8">
        <p class="text-gray-500">Loading products...</p>
      </div>
      <div v-else-if="error" class="text-center py-8">
        <p class="text-red-500">{{ error }}</p>
      </div>
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div v-for="product in products" :key="product.id" class="bg-white rounded-lg shadow-md overflow-hidden">
          <img :src="product.image" :alt="product.name" class="w-full h-48 object-cover" />
          <div class="p-4">
            <h3 class="text-lg font-semibold mb-2">{{ product.name }}</h3>
            <p class="text-gray-600 mb-2 line-clamp-2">{{ product.description }}</p>
            <div class="flex justify-between items-center">
              <span class="text-lg font-bold">${{ product.price.toFixed(2) }}</span>
              <button 
                @click="addToCart(product.id)" 
                class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
              >
                Add to Cart
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <section>
      <h2 class="text-2xl font-bold mb-6">Why Choose Us</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-md">
          <div class="text-blue-600 mb-4">
            <Icon name="mdi:truck-delivery" class="w-12 h-12" />
          </div>
          <h3 class="text-xl font-semibold mb-2">Fast Delivery</h3>
          <p class="text-gray-600">Get your products delivered to your doorstep quickly.</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md">
          <div class="text-blue-600 mb-4">
            <Icon name="mdi:shield-check" class="w-12 h-12" />
          </div>
          <h3 class="text-xl font-semibold mb-2">Secure Payments</h3>
          <p class="text-gray-600">Your payments are secure with our trusted payment gateways.</p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md">
          <div class="text-blue-600 mb-4">
            <Icon name="mdi:refresh" class="w-12 h-12" />
          </div>
          <h3 class="text-xl font-semibold mb-2">Easy Returns</h3>
          <p class="text-gray-600">Not satisfied? Return your products with ease.</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useProductStore } from '~/stores/products'
import { useCartStore } from '~/stores/cart'

const productStore = useProductStore()
const cartStore = useCartStore()

const { products, loading, error } = storeToRefs(productStore)

onMounted(() => {
  productStore.fetchProducts()
})

const addToCart = (productId) => {
  cartStore.addToCart(productId)
}
</script>