# NuxtMarkt
NuxtMarkt is a Nuxt 3 e-commerce template that allows you to create a fully functional online store with ease. It is built with the latest technologies and best practices in mind, ensuring a smooth and efficient development experience.

## Features
- Fully functional e-commerce template
- Built with Nuxt 3
- Responsive design
- Easy to customize
- SEO-friendly
- Supports multiple payment gateways
- User authentication
- Product management
- Order management
- Cart management
- Wishlist management
- Admin dashboard
- User dashboard
- Product reviews
- Product ratings
- Product search
- Product filtering
- Product sorting
- Product categories
- Product tags
- Product attributes

## Installation
To install NuxtMarkt, follow these steps:
1. Clone the repository:
   ```bash
   git clone https://github.com/maro14/nuxtmarkt.git
   ```
2. Navigate to the project directory:
   ```bash
   cd nuxtmarkt
   ```
3. Install the dependencies:
   ```bash
   npm install
   ```

## Configuration
To configure NuxtMarkt, you need to set up the environment variables. Create a `.env` file in the root directory and add the following variables:
```env
# Environment variables
# API URL
API_URL=http://localhost:3000/api
# JWT secret
JWT_SECRET=your_jwt_secret
# JWT expiration time