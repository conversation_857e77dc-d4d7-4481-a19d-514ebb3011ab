import { defineStore } from 'pinia'

interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
}

export const useUserStore = defineStore('user', {
  state: () => ({
    user: null as User | null,
    isAuthenticated: false,
    loading: false,
    error: null as string | null
  }),
  
  getters: {
    fullName: (state) => {
      if (!state.user) return ''
      return `${state.user.firstName} ${state.user.lastName}`
    }
  },
  
  actions: {
    async login(email: string, password: string) {
      this.loading = true
      this.error = null
      
      try {
        // In a real app, you would call your API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock successful login
        this.user = {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          email: email,
          phone: '(*************'
        }
        
        this.isAuthenticated = true
      } catch (error) {
        this.error = 'Invalid email or password'
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    
    logout() {
      this.user = null
      this.isAuthenticated = false
    },
    
    async register(userData: Partial<User> & { password: string }) {
      this.loading = true
      this.error = null
      
      try {
        // In a real app, you would call your API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock successful registration
        this.user = {
          id: '2',
          firstName: userData.firstName || '',
          lastName: userData.lastName || '',
          email: userData.email || '',
          phone: userData.phone || ''
        }
        
        this.isAuthenticated = true
      } catch (error) {
        this.error = 'Registration failed'
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    
    async updateProfile(userData: Partial<User>) {
      if (!this.user) return
      
      this.loading = true
      this.error = null
      
      try {
        // In a real app, you would call your API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Update user data
        this.user = {
          ...this.user,
          ...userData
        }
      } catch (error) {
        this.error = 'Failed to update profile'
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    
    async fetchUserData() {
      if (this.isAuthenticated) return
      
      this.loading = true
      this.error = null
      
      try {
        // Only access localStorage on client
        if (process.client) {
          // In a real app, you would check for stored tokens and fetch user data
          // For now, we'll just simulate a check
          const hasStoredSession = localStorage.getItem('user_session')
          
          if (hasStoredSession) {
            // Mock fetching user data
            await new Promise(resolve => setTimeout(resolve, 500))
          
            this.user = {
              id: '1',
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
              phone: '(*************'
            }
          
            this.isAuthenticated = true
          }
        }
      } catch (error) {
        this.error = 'Failed to restore session'
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    
    async changePassword(currentPassword: string, newPassword: string) {
      if (!this.user) return
      
      this.loading = true
      this.error = null
      
      try {
        // In a real app, you would call your API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Password change successful (mock)
        return true
      } catch (error) {
        this.error = 'Failed to change password'
        console.error(error)
        return false
      } finally {
        this.loading = false
      }
    }
  }
})