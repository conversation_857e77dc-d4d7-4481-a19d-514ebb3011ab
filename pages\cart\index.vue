<template>
  <div>
    <h1 class="text-3xl font-bold mb-8">Your Shopping Cart</h1>
    
    <div v-if="!cartItems.length" class="text-center py-12 bg-white rounded-lg shadow-md">
      <Icon name="mdi:cart-outline" class="w-16 h-16 mx-auto text-gray-400 mb-4" />
      <h2 class="text-2xl font-semibold mb-2">Your cart is empty</h2>
      <p class="text-gray-600 mb-6">Looks like you haven't added any products to your cart yet.</p>
      <NuxtLink to="/products" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition">
        Start Shopping
      </NuxtLink>
    </div>
    
    <div v-else>
      <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <table class="w-full">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200">
            <tr v-for="item in cartItems" :key="item.productId">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <img class="h-10 w-10 rounded-md object-cover" :src="item.product.image" :alt="item.product.name" />
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ item.product.name }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">${{ item.product.price.toFixed(2) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <button 
                    @click="decrementQuantity(item.productId)" 
                    class="bg-gray-200 px-2 py-1 rounded-l"
                    :disabled="item.quantity <= 1"
                  >
                    -
                  </button>
                  <input 
                    v-model="item.quantity" 
                    type="number" 
                    min="1" 
                    class="border-y border-gray-200 py-1 px-2 w-12 text-center" 
                    @change="updateQuantity(item.productId, item.quantity)"
                  />
                  <button 
                    @click="incrementQuantity(item.productId)" 
                    class="bg-gray-200 px-2 py-1 rounded-r"
                  >
                    +
                  </button>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">${{ (item.product.price * item.quantity).toFixed(2) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button @click="removeFromCart(item.productId)" class="text-red-600 hover:text-red-900">
                  <Icon name="mdi:delete" class="w-5 h-5" />
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between mb-2">
          <span class="text-gray-600">Subtotal</span>
          <span class="font-semibold">${{ cartTotal.toFixed(2) }}</span>
        </div>
        <div class="flex justify-between mb-2">
          <span class="text-gray-600">Shipping</span>
          <span class="font-semibold">$0.00</span>
        </div>
        <div class="flex justify-between mb-2">
          <span class="text-gray-600">Tax</span>
          <span class="font-semibold">${{ (cartTotal * 0.1).toFixed(2) }}</span>
        </div>
        <div class="border-t border-gray-200 my-4"></div>
        <div class="flex justify-between mb-6">
          <span class="text-lg font-bold">Total</span>
          <span class="text-lg font-bold">${{ (cartTotal + cartTotal * 0.1).toFixed(2) }}</span>
        </div>
        
        <NuxtLink to="/checkout" class="block bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition text-center">
          Proceed to Checkout
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useCartStore } from '~/stores/cart'

const cartStore = useCartStore()
const cartItems = storeToRefs(cartStore).items
const cartTotal = storeToRefs(cartStore).total

const updateQuantity = (productId, quantity) => {
  cartStore.updateQuantity(productId, parseInt(quantity))
}

const incrementQuantity = (productId) => {
  const item = cartItems.value.find(item => item.productId === productId)
  if (item) {
    cartStore.updateQuantity(productId, item.quantity + 1)
  }
}

const decrementQuantity = (productId) => {
  const item = cartItems.value.find(item => item.productId === productId)
  if (item && item.quantity > 1) {
    cartStore.updateQuantity(productId, item.quantity - 1)
  }
}

const removeFromCart = (productId) => {
  cartStore.removeFromCart(productId)
}
</script>