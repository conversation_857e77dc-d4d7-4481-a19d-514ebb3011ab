<template>
  <div>
    <div v-if="loading" class="text-center py-16">
      <div class="animate-pulse flex flex-col items-center">
        <div class="w-12 h-12 rounded-full bg-blue-200 mb-4"></div>
        <p class="text-gray-500 font-medium">Loading product...</p>
      </div>
    </div>
    <div v-else-if="!productData" class="text-center py-16 max-w-md mx-auto">
      <div class="bg-red-50 p-6 rounded-lg shadow-sm">
        <p class="text-red-500 font-medium mb-4">Product not found</p>
        <NuxtLink to="/products" class="inline-flex items-center text-blue-600 hover:text-blue-800 transition">
          <span class="mr-2">←</span> Back to Products
        </NuxtLink>
      </div>
    </div>
    <div v-else>
      <!-- Breadcrumb navigation -->
      <div class="mb-6 text-sm text-gray-500">
        <NuxtLink to="/" class="hover:text-blue-600 transition">Home</NuxtLink>
        <span class="mx-2">/</span>
        <NuxtLink to="/products" class="hover:text-blue-600 transition">Products</NuxtLink>
        <span class="mx-2">/</span>
        <span class="text-gray-700">{{ productData.name }}</span>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
        <!-- Product Image Section -->
        <div class="relative group">
          <div class="absolute inset-0 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl transform -rotate-1 scale-105 opacity-50 group-hover:opacity-70 transition-all duration-300"></div>
          <div class="relative overflow-hidden rounded-xl shadow-lg">
            <img 
              :src="productData.image" 
              :alt="productData.name" 
              class="w-full h-auto object-cover transform group-hover:scale-105 transition-transform duration-500" 
            />
          </div>
        </div>
        
        <!-- Product Info Section -->
        <div class="flex flex-col">
          <span class="text-blue-600 font-medium mb-2 uppercase tracking-wider text-sm">{{ productData.category }}</span>
          <h1 class="text-3xl font-bold mb-4 text-gray-800">{{ productData.name }}</h1>
          
          <div class="flex items-center mb-4">
            <div class="flex text-yellow-400">
              <Icon name="mdi:star" class="w-5 h-5" v-for="i in 5" :key="i" />
            </div>
            <span class="ml-2 text-gray-600 text-sm">(24 reviews)</span>
          </div>
          
          <p class="text-gray-600 mb-8 leading-relaxed">{{ productData.description }}</p>
          
          <div class="mb-8">
            <p class="text-3xl font-bold text-gray-800">${{ productData.price.toFixed(2) }}</p>
            <p class="text-green-600 text-sm mt-1">In stock & ready to ship</p>
          </div>
          
          <div class="mb-8">
            <label for="quantity" class="block text-gray-700 mb-2 font-medium">Quantity</label>
            <div class="flex items-center">
              <button 
                @click="decrementQuantity" 
                class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-l-lg transition"
                :disabled="quantity <= 1"
                :class="{'opacity-50 cursor-not-allowed': quantity <= 1}"
              >
                <Icon name="mdi:minus" class="w-4 h-4" />
              </button>
              <input 
                id="quantity" 
                v-model="quantity" 
                type="number" 
                min="1" 
                class="border-y border-gray-200 py-2 px-3 w-16 text-center focus:outline-none" 
              />
              <button 
                @click="incrementQuantity" 
                class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-r-lg transition"
              >
                <Icon name="mdi:plus" class="w-4 h-4" />
              </button>
            </div>
          </div>
          
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
            <button 
              @click="addToCart" 
              class="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition flex items-center justify-center"
            >
              <Icon name="mdi:cart" class="w-5 h-5 mr-2" />
              Add to Cart
            </button>
            
            <button 
              class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-50 transition flex items-center justify-center"
            >
              <Icon name="mdi:heart-outline" class="w-5 h-5 mr-2" />
              Save for Later
            </button>
          </div>
          
          <div class="border-t border-gray-200 pt-8 mt-auto">
            <div class="flex flex-col space-y-4">
              <div class="flex items-start">
                <Icon name="mdi:truck-delivery-outline" class="w-5 h-5 mr-3 text-gray-600 mt-0.5" />
                <div>
                  <h3 class="font-medium text-gray-800">Free Shipping</h3>
                  <p class="text-sm text-gray-600">Free standard shipping on orders over $50</p>
                </div>
              </div>
              
              <div class="flex items-start">
                <Icon name="mdi:refresh" class="w-5 h-5 mr-3 text-gray-600 mt-0.5" />
                <div>
                  <h3 class="font-medium text-gray-800">Easy Returns</h3>
                  <p class="text-sm text-gray-600">30-day return policy for eligible items</p>
                </div>
              </div>
              
              <div class="flex items-start">
                <Icon name="mdi:shield-check-outline" class="w-5 h-5 mr-3 text-gray-600 mt-0.5" />
                <div>
                  <h3 class="font-medium text-gray-800">Secure Checkout</h3>
                  <p class="text-sm text-gray-600">SSL encrypted payment processing</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useProductStore } from '~/stores/products'
import { useCartStore } from '~/stores/cart'

const route = useRoute()
const productId = parseInt(route.params.id)

const productStore = useProductStore()
const cartStore = useCartStore()

const { loading } = storeToRefs(productStore)
const productData = ref(null)

const quantity = ref(1)

onMounted(async () => {
  if (productStore.products.length === 0) {
    await productStore.fetchProducts()
  }
  
  const foundProduct = productStore.getProductById(productId)
  if (foundProduct) {
    productData.value = foundProduct
  }
})

const incrementQuantity = () => {
  quantity.value++
}

const decrementQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const addToCart = () => {
  if (productData.value) {
    cartStore.addToCart(productId, quantity.value)
  }
}
</script>