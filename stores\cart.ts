import { defineStore } from 'pinia'
import { useProductStore, type Product } from './products'

export interface CartItem {
  productId: number
  quantity: number
  product: Product
}

export const useCartStore = defineStore('cart', {
  state: () => ({
    items: [] as CartItem[]
  }),
  
  getters: {
    count: (state) => {
      return state.items.reduce((total, item) => total + item.quantity, 0)
    },
    
    total: (state) => {
      return state.items.reduce((total, item) => {
        return total + (item.product.price * item.quantity)
      }, 0)
    }
  },
  
  actions: {
    addToCart(productId: number, quantity = 1) {
      const productStore = useProductStore()
      const product = productStore.getProductById(productId)
      
      if (!product) return
      
      const existingItem = this.items.find(item => item.productId === productId)
      
      if (existingItem) {
        existingItem.quantity += quantity
      } else {
        this.items.push({
          productId,
          quantity,
          product
        })
      }
    },
    
    removeFromCart(productId: number) {
      const index = this.items.findIndex(item => item.productId === productId)
      if (index !== -1) {
        this.items.splice(index, 1)
      }
    },
    
    updateQuantity(productId: number, quantity: number) {
      const item = this.items.find(item => item.productId === productId)
      if (item) {
        item.quantity = quantity
      }
    },
    
    clearCart() {
      this.items = []
    }
  }
})