<template>
  <div>
    <h1 class="text-3xl font-bold mb-8">All Products</h1>
    
    <div class="flex flex-col md:flex-row gap-6 mb-8">
      <div class="md:w-1/4 bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-bold mb-4">Filters</h2>
        
        <div class="mb-6">
          <h3 class="font-semibold mb-2">Categories</h3>
          <div class="space-y-2">
            <label class="flex items-center">
              <input 
                type="checkbox" 
                v-model="filters.categories" 
                value="electronics" 
                class="mr-2"
              />
              Electronics
            </label>
            <label class="flex items-center">
              <input 
                type="checkbox" 
                v-model="filters.categories" 
                value="clothing" 
                class="mr-2"
              />
              Clothing
            </label>
          </div>
        </div>
        
        <div class="mb-6">
          <h3 class="font-semibold mb-2">Price Range</h3>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <label class="text-sm text-gray-600">Min</label>
              <input 
                type="number" 
                v-model="filters.minPrice" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div>
              <label class="text-sm text-gray-600">Max</label>
              <input 
                type="number" 
                v-model="filters.maxPrice" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
          </div>
        </div>
        
        <button 
          @click="resetFilters" 
          class="w-full bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300 transition"
        >
          Reset Filters
        </button>
      </div>
      
      <div class="md:w-3/4">
        <div v-if="loading" class="text-center py-8">
          <p class="text-gray-500">Loading products...</p>
        </div>
        <div v-else-if="error" class="text-center py-8">
          <p class="text-red-500">{{ error }}</p>
        </div>
        <div v-else-if="filteredProducts.length === 0" class="text-center py-8 bg-white rounded-lg shadow-md">
          <p class="text-gray-500">No products found matching your filters.</p>
        </div>
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <ProductCard 
            v-for="product in filteredProducts" 
            :key="product.id" 
            :product="product" 
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useProductStore } from '~/stores/products'

const productStore = useProductStore()
const { products, loading, error } = storeToRefs(productStore)

const filters = reactive({
  categories: [],
  minPrice: null,
  maxPrice: null
})

const filteredProducts = computed(() => {
  return products.value.filter(product => {
    // Filter by category
    if (filters.categories.length > 0 && !filters.categories.includes(product.category)) {
      return false
    }
    
    // Filter by min price
    if (filters.minPrice && product.price < filters.minPrice) {
      return false
    }
    
    // Filter by max price
    if (filters.maxPrice && product.price > filters.maxPrice) {
      return false
    }
    
    return true
  })
})

const resetFilters = () => {
  filters.categories = []
  filters.minPrice = null
  filters.maxPrice = null
}

onMounted(() => {
  if (productStore.products.length === 0) {
    productStore.fetchProducts()
  }
})
</script>