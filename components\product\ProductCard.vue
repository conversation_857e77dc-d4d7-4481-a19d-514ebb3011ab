<template>
  <div class="bg-white rounded-lg shadow-md overflow-hidden">
    <NuxtLink :to="`/product/${product.id}`">
      <img :src="product.image" :alt="product.name" class="w-full h-48 object-cover" />
    </NuxtLink>
    <div class="p-4">
      <NuxtLink :to="`/product/${product.id}`">
        <h3 class="text-lg font-semibold mb-2 hover:text-blue-600">{{ product.name }}</h3>
      </NuxtLink>
      <p class="text-gray-600 mb-2 line-clamp-2">{{ product.description }}</p>
      <div class="flex justify-between items-center">
        <span class="text-lg font-bold">${{ product.price.toFixed(2) }}</span>
        <button 
          @click="addToCart" 
          class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
        >
          Add to Cart
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useCartStore } from '~/stores/cart'

const props = defineProps({
  product: {
    type: Object,
    required: true
  }
})

const cartStore = useCartStore()

const addToCart = () => {
  cartStore.addToCart(props.product.id)
}
</script>