// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: [
    '@pinia/nuxt',
    '@nuxtjs/tailwindcss',
    '@nuxt/icon', // Updated from 'nuxt-icon' to '@nuxt/icon'
    '@vueuse/nuxt'
  ],
  app: {
    head: {
      title: 'NuxtMarkt - Your E-commerce Solution',
      meta: [
        { name: 'description', content: 'A modern e-commerce platform built with Nuxt 3' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  }
})
