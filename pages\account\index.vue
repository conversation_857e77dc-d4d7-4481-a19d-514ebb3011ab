<template>
  <div>
    <h1 class="text-3xl font-bold mb-8">My Account</h1>
    
    <div v-if="!isLoggedIn" class="bg-white rounded-lg shadow-md p-6">
      <h2 class="text-xl font-bold mb-4">Login</h2>
      <form @submit.prevent="login">
        <div class="mb-4">
          <label for="email" class="block text-gray-700 mb-2">Email</label>
          <input 
            id="email" 
            v-model="loginForm.email" 
            type="email" 
            class="w-full border border-gray-300 rounded px-3 py-2" 
            required
          />
        </div>
        <div class="mb-6">
          <label for="password" class="block text-gray-700 mb-2">Password</label>
          <input 
            id="password" 
            v-model="loginForm.password" 
            type="password" 
            class="w-full border border-gray-300 rounded px-3 py-2" 
            required
          />
        </div>
        <button 
          type="submit" 
          class="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition"
          :disabled="isSubmitting"
        >
          {{ isSubmitting ? 'Logging in...' : 'Login' }}
        </button>
      </form>
      
      <div class="mt-6 pt-6 border-t border-gray-200">
        <h2 class="text-xl font-bold mb-4">Create Account</h2>
        <p class="text-gray-600 mb-4">Don't have an account yet? Register now to start shopping.</p>
        <button 
          @click="showRegisterForm = true" 
          class="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-bold hover:bg-gray-300 transition"
        >
          Register
        </button>
      </div>
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="md:col-span-1">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Account Navigation</h2>
          <ul class="space-y-2">
            <li>
              <button 
                @click="activeTab = 'profile'" 
                class="w-full text-left px-4 py-2 rounded transition"
                :class="activeTab === 'profile' ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100'"
              >
                Profile
              </button>
            </li>
            <li>
              <button 
                @click="activeTab = 'orders'" 
                class="w-full text-left px-4 py-2 rounded transition"
                :class="activeTab === 'orders' ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100'"
              >
                Orders
              </button>
            </li>
            <li>
              <button 
                @click="activeTab = 'addresses'" 
                class="w-full text-left px-4 py-2 rounded transition"
                :class="activeTab === 'addresses' ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100'"
              >
                Addresses
              </button>
            </li>
            <li>
              <button 
                @click="logout" 
                class="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 rounded transition"
              >
                Logout
              </button>
            </li>
          </ul>
        </div>
      </div>
      
      <div class="md:col-span-2">
        <div v-if="activeTab === 'profile'" class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Profile</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-gray-700 mb-2">First Name</label>
              <input 
                v-model="userProfile.firstName" 
                type="text" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div>
              <label class="block text-gray-700 mb-2">Last Name</label>
              <input 
                v-model="userProfile.lastName" 
                type="text" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
            <div class="md:col-span-2">
              <label class="block text-gray-700 mb-2">Email</label>
              <input 
                v-model="userProfile.email" 
                type="email" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
                disabled
              />
            </div>
            <div>
              <label class="block text-gray-700 mb-2">Phone</label>
              <input 
                v-model="userProfile.phone" 
                type="tel" 
                class="w-full border border-gray-300 rounded px-3 py-2" 
              />
            </div>
          </div>
          <div class="mt-6">
            <button 
              @click="saveProfile" 
              class="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition"
            >
              Save Changes
            </button>
          </div>
        </div>
        
        <div v-if="activeTab === 'orders'" class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Order History</h2>
          <div v-if="orders.length === 0" class="text-center py-6">
            <p class="text-gray-500">You haven't placed any orders yet.</p>
            <NuxtLink to="/products" class="text-blue-600 hover:underline mt-2 inline-block">
              Start Shopping
            </NuxtLink>
          </div>
          <div v-else class="space-y-4">
            <div v-for="order in orders" :key="order.id" class="border border-gray-200 rounded-lg p-4">
              <div class="flex justify-between items-center mb-2">
                <span class="font-semibold">Order #{{ order.id }}</span>
                <span class="text-sm text-gray-600">{{ order.date }}</span>
              </div>
              <div class="flex justify-between items-center mb-2">
                <span>Status:</span>
                <span 
                  class="px-2 py-1 rounded text-sm"
                  :class="{
                    'bg-green-100 text-green-800': order.status === 'Delivered',
                    'bg-blue-100 text-blue-800': order.status === 'Shipped',
                    'bg-yellow-100 text-yellow-800': order.status === 'Processing'
                  }"
                >
                  {{ order.status }}
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span>Total:</span>
                <span class="font-semibold">${{ order.total.toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-if="activeTab === 'addresses'" class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold mb-4">Saved Addresses</h2>
          <div v-if="addresses.length === 0" class="text-center py-6">
            <p class="text-gray-500">You don't have any saved addresses yet.</p>
          </div>
          <div v-else class="space-y-4">
            <div v-for="address in addresses" :key="address.id" class="border border-gray-200 rounded-lg p-4">
              <div class="flex justify-between items-start">
                <div>
                  <p class="font-semibold">{{ address.name }}</p>
                  <p>{{ address.street }}</p>
                  <p>{{ address.city }}, {{ address.state }} {{ address.zipCode }}</p>
                  <p>{{ address.country }}</p>
                </div>
                <div class="flex space-x-2">
                  <button class="text-blue-600 hover:text-blue-800">
                    <Icon name="mdi:pencil" class="w-5 h-5" />
                  </button>
                  <button class="text-red-600 hover:text-red-800">
                    <Icon name="mdi:delete" class="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-6">
            <button 
              class="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition"
            >
              Add New Address
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuth } from '~/composables/useAuth'

// Use our auth composable
const { 
  user, 
  isAuthenticated, 
  loading, 
  error, 
  login, 
  logout, 
  updateProfile 
} = useAuth()

const isSubmitting = ref(false)
const showRegisterForm = ref(false)
const activeTab = ref('profile')

const loginForm = reactive({
  email: '',
  password: ''
})

const registerForm = reactive({
  firstName: '',
  lastName: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const userProfile = computed(() => {
  if (!user.value) return {
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  }
  
  return {
    firstName: user.value.firstName,
    lastName: user.value.lastName,
    email: user.value.email,
    phone: user.value.phone
  }
})

// Mock data for orders and addresses
const orders = ref([
  {
    id: '1001',
    date: '2023-05-15',
    status: 'Delivered',
    total: 129.99
  },
  {
    id: '1002',
    date: '2023-06-22',
    status: 'Shipped',
    total: 79.95
  },
  {
    id: '1003',
    date: '2023-07-10',
    status: 'Processing',
    total: 249.99
  }
])

const addresses = ref([
  {
    id: 1,
    name: 'Home',
    street: '123 Main St',
    city: 'Anytown',
    state: 'CA',
    zipCode: '12345',
    country: 'United States'
  },
  {
    id: 2,
    name: 'Work',
    street: '456 Office Blvd',
    city: 'Business City',
    state: 'NY',
    zipCode: '67890',
    country: 'United States'
  }
])

const handleLogin = async () => {
  isSubmitting.value = true
  
  try {
    await login(loginForm.email, loginForm.password)
  } finally {
    isSubmitting.value = false
  }
}

const handleRegister = async () => {
  isSubmitting.value = true
  
  try {
    // Validate passwords match
    if (registerForm.password !== registerForm.confirmPassword) {
      alert('Passwords do not match')
      return
    }
    
    // Call register from auth composable
    await register(registerForm)
  } finally {
    isSubmitting.value = false
  }
}

const handleLogout = () => {
  logout()
}

const saveProfile = async () => {
  try {
    await updateProfile(userProfile.value)
    alert('Profile saved successfully!')
  } catch (error) {
    alert('Failed to save profile')
  }
}

// Define middleware to protect this route
definePageMeta({
  middleware: ['auth']
})
</script>