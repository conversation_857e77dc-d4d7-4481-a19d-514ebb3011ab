import { useUserStore } from '~/stores/user'

export function useAuth() {
  const userStore = useUserStore()
  const router = useRouter()
  
  const login = async (email: string, password: string) => {
    await userStore.login(email, password)
    
    if (userStore.isAuthenticated) {
      // Store session in localStorage (in a real app, you'd use secure cookies)
      localStorage.setItem('user_session', 'true')
      
      // Redirect to account page
      router.push('/account')
      return true
    }
    
    return false
  }
  
  const logout = () => {
    userStore.logout()
    localStorage.removeItem('user_session')
    router.push('/')
  }
  
  const register = async (userData: any) => {
    await userStore.register(userData)
    
    if (userStore.isAuthenticated) {
      localStorage.setItem('user_session', 'true')
      router.push('/account')
      return true
    }
    
    return false
  }
  
  const requireAuth = async (to: string) => {
    // If not authenticated, redirect to login
    if (!userStore.isAuthenticated) {
      router.push(`/login?redirect=${encodeURIComponent(to)}`)
      return false
    }
    
    return true
  }
  
  return {
    user: computed(() => userStore.user),
    isAuthenticated: computed(() => userStore.isAuthenticated),
    loading: computed(() => userStore.loading),
    error: computed(() => userStore.error),
    login,
    logout,
    register,
    requireAuth,
    updateProfile: userStore.updateProfile,
    changePassword: userStore.changePassword
  }
}