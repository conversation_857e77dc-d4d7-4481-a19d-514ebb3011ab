<template>
  <header class="bg-white shadow-md">
    <div class="container mx-auto px-4 py-4 flex justify-between items-center">
      <NuxtLink to="/" class="text-2xl font-bold text-gray-800">NuxtMarkt</NuxtLink>
      
      <div class="flex items-center space-x-4">
        <NuxtLink to="/products" class="text-gray-600 hover:text-gray-900">Products</NuxtLink>
        <NuxtLink to="/cart" class="text-gray-600 hover:text-gray-900 flex items-center">
          <Icon name="mdi:cart" class="w-5 h-5 mr-1" />
          <span>Cart ({{ cartCount }})</span>
        </NuxtLink>
        <NuxtLink to="/account" class="text-gray-600 hover:text-gray-900">
          <Icon name="mdi:account" class="w-5 h-5" />
        </NuxtLink>
      </div>
    </div>
  </header>
</template>

<script setup>
import { useCartStore } from '~/stores/cart' // Import the cart store
import { storeToRefs } from 'pinia' // Import storeToRefs for reactivity

const cartStore = useCartStore()
const { itemCount: cartCount } = storeToRefs(cartStore) // Get the reactive item count
</script>